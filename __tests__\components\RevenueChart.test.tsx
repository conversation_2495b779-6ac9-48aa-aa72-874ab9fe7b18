import { render, screen, fireEvent } from '@testing-library/react'
import { RevenueChart } from '@/components/dashboard/RevenueChart'
import { ChartData } from '@/types'

// Mock recharts
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  BarChart: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="bar-chart">{children}</div>
  ),
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
}))

describe('RevenueChart', () => {
  const mockData: ChartData[] = [
    { month: 'Jan', value: 320000 },
    { month: 'Feb', value: 450000 },
    { month: 'Mar', value: 380000 },
  ]

  const mockRevenue = {
    current: 0,
    change: -100,
    period: 'vs Last 7 days',
  }

  const mockOnPeriodChange = jest.fn()

  const defaultProps = {
    data: mockData,
    revenue: mockRevenue,
    selectedPeriod: 'last7days' as const,
    onPeriodChange: mockOnPeriodChange,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders revenue chart with correct title', () => {
    render(<RevenueChart {...defaultProps} />)
    
    expect(screen.getByText('Revenue')).toBeInTheDocument()
    expect(screen.getByText('-100%')).toBeInTheDocument()
    expect(screen.getByText('vs Last 7 days')).toBeInTheDocument()
  })

  it('renders period selection buttons', () => {
    render(<RevenueChart {...defaultProps} />)
    
    expect(screen.getByRole('button', { name: 'Today' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Last 7 days' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Last 30 days' })).toBeInTheDocument()
  })

  it('highlights selected period button', () => {
    render(<RevenueChart {...defaultProps} />)
    
    const last7DaysButton = screen.getByRole('button', { name: 'Last 7 days' })
    expect(last7DaysButton).toHaveClass('bg-primary')
  })

  it('calls onPeriodChange when period button is clicked', () => {
    render(<RevenueChart {...defaultProps} />)
    
    const todayButton = screen.getByRole('button', { name: 'Today' })
    fireEvent.click(todayButton)
    
    expect(mockOnPeriodChange).toHaveBeenCalledWith('today')
  })

  it('formats currency correctly', () => {
    const propsWithRevenue = {
      ...defaultProps,
      revenue: { ...mockRevenue, current: 1500000 },
    }
    
    render(<RevenueChart {...propsWithRevenue} />)
    
    expect(screen.getByText('₦1,500,000.00')).toBeInTheDocument()
  })

  it('renders chart components', () => {
    render(<RevenueChart {...defaultProps} />)
    
    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument()
    expect(screen.getByTestId('bar')).toBeInTheDocument()
    expect(screen.getByTestId('x-axis')).toBeInTheDocument()
    expect(screen.getByTestId('y-axis')).toBeInTheDocument()
  })
})
