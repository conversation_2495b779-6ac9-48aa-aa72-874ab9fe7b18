import { render, screen, fireEvent } from '@testing-library/react'
import { MetricsCard } from '@/components/dashboard/MetricsCard'

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
})

describe('MetricsCard', () => {
  const mockProps = {
    bank: 'STERLING BANK',
    accountNumber: '**********',
    businessName: 'OGEDENGBE FRUITS STORE',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders account details correctly', () => {
    render(<MetricsCard {...mockProps} />)
    
    expect(screen.getByText('ACCOUNT DETAILS')).toBeInTheDocument()
    expect(screen.getByText('STERLING BANK')).toBeInTheDocument()
    expect(screen.getByText('**********')).toBeInTheDocument()
    expect(screen.getByText('OGEDENGBE FRUITS STORE')).toBeInTheDocument()
  })

  it('renders copy button', () => {
    render(<MetricsCard {...mockProps} />)
    
    const copyButton = screen.getByRole('button', { name: /copy/i })
    expect(copyButton).toBeInTheDocument()
  })

  it('copies account number to clipboard when copy button is clicked', async () => {
    const mockWriteText = navigator.clipboard.writeText as jest.Mock
    
    render(<MetricsCard {...mockProps} />)
    
    const copyButton = screen.getByRole('button', { name: /copy/i })
    fireEvent.click(copyButton)
    
    expect(mockWriteText).toHaveBeenCalledWith('**********')
  })

  it('applies correct CSS classes', () => {
    render(<MetricsCard {...mockProps} />)
    
    const card = screen.getByText('ACCOUNT DETAILS').closest('.fundr-card')
    expect(card).toHaveClass('fundr-card')
  })
})
