import { render, screen, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import Dashboard from '@/app/page'
import dashboardReducer from '@/store/slices/dashboardSlice'
import transactionsReducer from '@/store/slices/transactionsSlice'
import { mockApi } from '@/store/api/mockApi'

// Mock the API
jest.mock('@/store/api/mockApi')
const mockedApi = mockApi as jest.Mocked<typeof mockApi>

// Mock recharts
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  BarChart: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="bar-chart">{children}</div>
  ),
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
}))

const createTestStore = () => {
  return configureStore({
    reducer: {
      dashboard: dashboardReducer,
      transactions: transactionsReducer,
    },
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createTestStore()
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  )
}

describe('Dashboard Page', () => {
  const mockDashboardData = {
    metrics: {
      revenue: {
        current: 1500000,
        change: -100,
        period: 'vs Last 7 days',
      },
      totalValue: 1500000,
      accountDetails: {
        bank: 'STERLING BANK',
        accountNumber: '**********',
        businessName: 'OGEDENGBE FRUITS STORE',
      },
    },
    chartData: [
      { month: 'Jan', value: 320000 },
      { month: 'Feb', value: 450000 },
      { month: 'Mar', value: 380000 },
    ],
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    mockedApi.getDashboardData.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderWithProvider(<Dashboard />)
    
    expect(screen.getByText('Loading dashboard...')).toBeInTheDocument()
  })

  it('renders dashboard content after successful data fetch', async () => {
    mockedApi.getDashboardData.mockResolvedValue({
      success: true,
      data: mockDashboardData,
    })

    renderWithProvider(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText('Online Payments')).toBeInTheDocument()
    })

    // Check if MetricsCard is rendered
    expect(screen.getByText('STERLING BANK')).toBeInTheDocument()
    expect(screen.getByText('**********')).toBeInTheDocument()
    expect(screen.getByText('OGEDENGBE FRUITS STORE')).toBeInTheDocument()

    // Check if RevenueChart is rendered
    expect(screen.getByText('Revenue')).toBeInTheDocument()
    expect(screen.getByText('-100%')).toBeInTheDocument()
    expect(screen.getByText('vs Last 7 days')).toBeInTheDocument()
  })

  it('renders error state when API fails', async () => {
    const errorMessage = 'Failed to fetch dashboard data'
    mockedApi.getDashboardData.mockRejectedValue(new Error(errorMessage))

    renderWithProvider(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument()
    })
  })

  it('calls API with correct period on mount', async () => {
    mockedApi.getDashboardData.mockResolvedValue({
      success: true,
      data: mockDashboardData,
    })

    renderWithProvider(<Dashboard />)
    
    await waitFor(() => {
      expect(mockedApi.getDashboardData).toHaveBeenCalledWith('last7days')
    })
  })

  it('renders no data state when data is null', async () => {
    mockedApi.getDashboardData.mockResolvedValue({
      success: true,
      data: null as any,
    })

    renderWithProvider(<Dashboard />)
    
    await waitFor(() => {
      expect(screen.getByText('No data available')).toBeInTheDocument()
    })
  })
})
