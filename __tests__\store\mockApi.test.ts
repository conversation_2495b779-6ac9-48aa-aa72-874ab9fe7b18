import { mockApi } from '@/store/api/mockApi'
import { TransactionFilters } from '@/types'

describe('mockApi', () => {
  describe('getDashboardData', () => {
    it('should return dashboard data with correct structure', async () => {
      const result = await mockApi.getDashboardData('last7days')
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('metrics')
      expect(result.data).toHaveProperty('chartData')
      
      // Check metrics structure
      expect(result.data.metrics).toHaveProperty('revenue')
      expect(result.data.metrics).toHaveProperty('totalValue')
      expect(result.data.metrics).toHaveProperty('accountDetails')
      
      // Check revenue structure
      expect(result.data.metrics.revenue).toHaveProperty('current')
      expect(result.data.metrics.revenue).toHaveProperty('change')
      expect(result.data.metrics.revenue).toHaveProperty('period')
      
      // Check account details structure
      expect(result.data.metrics.accountDetails).toHaveProperty('bank')
      expect(result.data.metrics.accountDetails).toHaveProperty('accountNumber')
      expect(result.data.metrics.accountDetails).toHaveProperty('businessName')
      
      // Check chart data is array
      expect(Array.isArray(result.data.chartData)).toBe(true)
      expect(result.data.chartData.length).toBeGreaterThan(0)
      
      // Check chart data structure
      result.data.chartData.forEach(item => {
        expect(item).toHaveProperty('month')
        expect(item).toHaveProperty('value')
        expect(typeof item.month).toBe('string')
        expect(typeof item.value).toBe('number')
      })
    })

    it('should simulate network delay', async () => {
      const startTime = Date.now()
      await mockApi.getDashboardData('last7days')
      const endTime = Date.now()
      
      // Should take at least 500ms due to simulated delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(500)
    })

    it('should return consistent data structure regardless of period', async () => {
      const periods = ['today', 'last7days', 'last30days']
      
      for (const period of periods) {
        const result = await mockApi.getDashboardData(period)
        
        expect(result.success).toBe(true)
        expect(result.data).toHaveProperty('metrics')
        expect(result.data).toHaveProperty('chartData')
      }
    })
  })

  describe('getTransactions', () => {
    const mockFilters: TransactionFilters = {
      dateRange: {
        from: new Date(2023, 5, 6),
        to: new Date(2023, 5, 15),
      },
      account: 'All Accounts',
    }

    it('should return transactions data with correct structure', async () => {
      const result = await mockApi.getTransactions(1, mockFilters)
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('transactions')
      expect(result.data).toHaveProperty('pagination')
      
      // Check transactions structure
      expect(Array.isArray(result.data.transactions)).toBe(true)
      
      result.data.transactions.forEach(transaction => {
        expect(transaction).toHaveProperty('id')
        expect(transaction).toHaveProperty('amount')
        expect(transaction).toHaveProperty('transactionId')
        expect(transaction).toHaveProperty('type')
        expect(transaction).toHaveProperty('date')
        expect(transaction).toHaveProperty('time')
        expect(transaction).toHaveProperty('status')
        
        expect(typeof transaction.id).toBe('string')
        expect(typeof transaction.amount).toBe('number')
        expect(typeof transaction.transactionId).toBe('string')
        expect(['Transfer', 'Withdrawal', 'Deposit', 'Request']).toContain(transaction.type)
        expect(['Processed', 'Failed']).toContain(transaction.status)
      })
      
      // Check pagination structure
      expect(result.data.pagination).toHaveProperty('currentPage')
      expect(result.data.pagination).toHaveProperty('totalPages')
      expect(result.data.pagination).toHaveProperty('totalItems')
      expect(result.data.pagination).toHaveProperty('itemsPerPage')
    })

    it('should handle pagination correctly', async () => {
      const page1 = await mockApi.getTransactions(1, mockFilters)
      const page2 = await mockApi.getTransactions(2, mockFilters)
      
      expect(page1.data.pagination.currentPage).toBe(1)
      expect(page2.data.pagination.currentPage).toBe(2)
      
      // Should have different transactions (or empty for page 2)
      if (page2.data.transactions.length > 0) {
        expect(page1.data.transactions[0].id).not.toBe(page2.data.transactions[0].id)
      }
    })

    it('should respect itemsPerPage limit', async () => {
      const result = await mockApi.getTransactions(1, mockFilters)
      
      expect(result.data.transactions.length).toBeLessThanOrEqual(6) // itemsPerPage = 6
    })

    it('should simulate network delay', async () => {
      const startTime = Date.now()
      await mockApi.getTransactions(1, mockFilters)
      const endTime = Date.now()
      
      // Should take at least 300ms due to simulated delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(300)
    })

    it('should calculate pagination correctly', async () => {
      const result = await mockApi.getTransactions(1, mockFilters)
      const { pagination } = result.data
      
      expect(pagination.totalPages).toBe(Math.ceil(pagination.totalItems / pagination.itemsPerPage))
      expect(pagination.currentPage).toBeGreaterThanOrEqual(1)
      expect(pagination.currentPage).toBeLessThanOrEqual(pagination.totalPages)
    })
  })
})
