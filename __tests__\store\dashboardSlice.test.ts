import { configureStore } from '@reduxjs/toolkit'
import dashboardReducer, {
  setSelectedPeriod,
  clearError,
  fetchDashboardData,
} from '@/store/slices/dashboardSlice'
import { mockApi } from '@/store/api/mockApi'

// Mock the API
jest.mock('@/store/api/mockApi')
const mockedApi = mockApi as jest.Mocked<typeof mockApi>

describe('dashboardSlice', () => {
  let store: ReturnType<typeof configureStore>

  beforeEach(() => {
    store = configureStore({
      reducer: {
        dashboard: dashboardReducer,
      },
    })
    jest.clearAllMocks()
  })

  describe('reducers', () => {
    it('should handle setSelectedPeriod', () => {
      store.dispatch(setSelectedPeriod('today'))
      
      const state = store.getState().dashboard
      expect(state.selectedPeriod).toBe('today')
    })

    it('should handle clearError', () => {
      // First set an error state
      const initialState = {
        data: null,
        isLoading: false,
        error: 'Some error',
        selectedPeriod: 'last7days' as const,
      }
      
      const action = clearError()
      const newState = dashboardReducer(initialState, action)
      
      expect(newState.error).toBeNull()
    })
  })

  describe('fetchDashboardData async thunk', () => {
    const mockDashboardData = {
      metrics: {
        revenue: {
          current: 0,
          change: -100,
          period: 'vs Last 7 days',
        },
        totalValue: 0,
        accountDetails: {
          bank: 'STERLING BANK',
          accountNumber: '**********',
          businessName: 'OGEDENGBE FRUITS STORE',
        },
      },
      chartData: [
        { month: 'Jan', value: 320000 },
        { month: 'Feb', value: 450000 },
      ],
    }

    it('should handle successful API call', async () => {
      mockedApi.getDashboardData.mockResolvedValue({
        success: true,
        data: mockDashboardData,
      })

      await store.dispatch(fetchDashboardData('last7days'))
      
      const state = store.getState().dashboard
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.data).toEqual(mockDashboardData)
    })

    it('should handle API error', async () => {
      const errorMessage = 'API Error'
      mockedApi.getDashboardData.mockRejectedValue(new Error(errorMessage))

      await store.dispatch(fetchDashboardData('last7days'))
      
      const state = store.getState().dashboard
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe(errorMessage)
      expect(state.data).toBeNull()
    })

    it('should set loading state during API call', () => {
      mockedApi.getDashboardData.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      store.dispatch(fetchDashboardData('last7days'))
      
      const state = store.getState().dashboard
      expect(state.isLoading).toBe(true)
      expect(state.error).toBeNull()
    })
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().dashboard
      
      expect(state.data).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.selectedPeriod).toBe('last7days')
    })
  })
})
