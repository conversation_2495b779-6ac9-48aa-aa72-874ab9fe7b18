import { configureStore } from '@reduxjs/toolkit'
import transactionsReducer, {
  setFilters,
  clearError,
  fetchTransactions,
} from '@/store/slices/transactionsSlice'
import { mockApi } from '@/store/api/mockApi'
import { TransactionFilters } from '@/types'

// Mock the API
jest.mock('@/store/api/mockApi')
const mockedApi = mockApi as jest.Mocked<typeof mockApi>

describe('transactionsSlice', () => {
  let store: ReturnType<typeof configureStore>

  beforeEach(() => {
    store = configureStore({
      reducer: {
        transactions: transactionsReducer,
      },
    })
    jest.clearAllMocks()
  })

  describe('reducers', () => {
    it('should handle setFilters', () => {
      const newFilters: Partial<TransactionFilters> = {
        account: 'Sterling Bank',
        status: 'Processed',
      }
      
      store.dispatch(setFilters(newFilters))
      
      const state = store.getState().transactions
      expect(state.filters.account).toBe('Sterling Bank')
      expect(state.filters.status).toBe('Processed')
    })

    it('should handle clearError', () => {
      const initialState = {
        data: null,
        isLoading: false,
        error: 'Some error',
        filters: {
          dateRange: {
            from: new Date(2023, 5, 6),
            to: new Date(2023, 5, 15),
          },
          account: 'All Accounts',
        },
      }
      
      const action = clearError()
      const newState = transactionsReducer(initialState, action)
      
      expect(newState.error).toBeNull()
    })
  })

  describe('fetchTransactions async thunk', () => {
    const mockTransactionsData = {
      transactions: [
        {
          id: '1',
          amount: 43644,
          transactionId: 'TR_8401857902',
          type: 'Transfer' as const,
          date: 'Feb 12, 2022',
          time: '10:30AM',
          status: 'Processed' as const,
        },
      ],
      pagination: {
        currentPage: 1,
        totalPages: 2,
        totalItems: 7,
        itemsPerPage: 6,
      },
    }

    it('should handle successful API call', async () => {
      mockedApi.getTransactions.mockResolvedValue({
        success: true,
        data: mockTransactionsData,
      })

      const filters: TransactionFilters = {
        dateRange: { from: new Date(), to: new Date() },
        account: 'All Accounts',
      }

      await store.dispatch(fetchTransactions({ page: 1, filters }))
      
      const state = store.getState().transactions
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.data).toEqual(mockTransactionsData)
    })

    it('should handle API error', async () => {
      const errorMessage = 'API Error'
      mockedApi.getTransactions.mockRejectedValue(new Error(errorMessage))

      const filters: TransactionFilters = {
        dateRange: { from: new Date(), to: new Date() },
        account: 'All Accounts',
      }

      await store.dispatch(fetchTransactions({ page: 1, filters }))
      
      const state = store.getState().transactions
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe(errorMessage)
      expect(state.data).toBeNull()
    })

    it('should set loading state during API call', () => {
      mockedApi.getTransactions.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const filters: TransactionFilters = {
        dateRange: { from: new Date(), to: new Date() },
        account: 'All Accounts',
      }

      store.dispatch(fetchTransactions({ page: 1, filters }))
      
      const state = store.getState().transactions
      expect(state.isLoading).toBe(true)
      expect(state.error).toBeNull()
    })
  })
})
